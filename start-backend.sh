#!/bin/bash

# Start Superset backend with hot reload for development

echo "🚀 Starting Superset Backend with Hot Reload..."
echo "📍 Backend will be available at: http://localhost:8088"
echo "👤 Admin credentials: admin / admin123"
echo ""

# Activate virtual environment
source venv/bin/activate

# Set development configuration
export SUPERSET_CONFIG_PATH=/Users/<USER>/Documents/repos/zomato/superset-internal/superset_config_dev.py

# Set Flask environment for development
export FLASK_ENV=development
export FLASK_DEBUG=1

# Start Superset with hot reload
echo "Starting Superset backend server..."
superset run -h 0.0.0.0 -p 8088 --with-threads --reload --debugger
