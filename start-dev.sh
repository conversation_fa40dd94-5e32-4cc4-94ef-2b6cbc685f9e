#!/bin/bash

# Start both Superset backend and frontend with hot reload for development

echo "🚀 Starting Superset Development Environment"
echo "============================================="
echo ""
echo "This will start both backend and frontend servers:"
echo "📍 Backend:  http://localhost:8088"
echo "📍 Frontend: http://localhost:9000"
echo "👤 Admin credentials: admin / admin123"
echo ""
echo "Press Ctrl+C to stop both servers"
echo ""

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Stopping development servers..."
    kill $(jobs -p) 2>/dev/null
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start backend in background
echo "🔧 Starting backend server..."
./start-backend.sh &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Start frontend in background
echo "🎨 Starting frontend server..."
./start-frontend.sh &
FRONTEND_PID=$!

# Wait for both processes
wait $BACKEND_PID $FRONTEND_PID
