# Development configuration for Superset
from cachelib.file import FileSystemCache

# Basic configuration - use a strong secret key for development
SECRET_KEY = 'dev-secret-key-change-this-in-production-12345678901234567890'

# SQLite database for development
SQLALCHEMY_DATABASE_URI = 'sqlite:///superset.db'

# Development settings
DEBUG = True
SUPERSET_WEBSERVER_PORT = 8088
SUPERSET_WEBSERVER_TIMEOUT = 60

# Cache configuration for development
CACHE_CONFIG = {
    'CACHE_TYPE': 'SimpleCache',
    'CACHE_DEFAULT_TIMEOUT': 300
}

# Results backend for SQL Lab
RESULTS_BACKEND = FileSystemCache('/tmp/superset_cache', threshold=1000)

# Feature flags for development
FEATURE_FLAGS = {
    'ENABLE_TEMPLATE_PROCESSING': True,
    'DASHBOARD_RBAC': False,
    'THUMBNAILS': False,
    'ALERT_REPORTS': False,
}

# Allow CORS for frontend development
ENABLE_CORS = True
CORS_OPTIONS = {
    'supports_credentials': True,
    'allow_headers': ['*'],
    'resources': ['*'],
    'origins': ['http://localhost:9000', 'http://127.0.0.1:9000']
}

# Disable CSRF for development
WTF_CSRF_ENABLED = False

# Logging
LOG_LEVEL = 'DEBUG'

# Disable some security features for development
TALISMAN_ENABLED = False

# Hot reload settings
SEND_FILE_MAX_AGE_DEFAULT = 0
