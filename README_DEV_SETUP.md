# Superset Development Setup with Hot Reload

This guide provides instructions for setting up Superset locally with hot reload for faster development.

## 🚀 Quick Start

### Prerequisites
- Python 3.10+ 
- Node.js 16+ and npm
- Git

### Setup Complete!
The development environment has been set up with the following components:

1. **Python Virtual Environment**: `venv/` directory
2. **Database**: SQLite database (`superset.db`) 
3. **Admin User**: Created with credentials `admin` / `admin123`
4. **Frontend Dependencies**: Installed in `superset-frontend/`
5. **Development Configuration**: `superset_config_dev.py`

## 🎯 Running the Development Environment

### Option 1: Start Both Servers Together
```bash
./start-dev.sh
```
This will start both backend and frontend servers simultaneously.

### Option 2: Start Servers Separately

#### Backend Only
```bash
./start-backend.sh
```
- Backend will be available at: http://localhost:8088
- Hot reload enabled for Python code changes
- Debug mode enabled

#### Frontend Only  
```bash
./start-frontend.sh
```
- Frontend will be available at: http://localhost:9000
- Hot reload enabled for React/TypeScript changes
- Webpack dev server with HMR

## 📍 Access Points

- **Frontend (Development)**: http://localhost:9000
- **Backend API**: http://localhost:8088
- **Admin Credentials**: 
  - Username: `admin`
  - Password: `admin123`

## 🔧 Development Features

### Hot Reload Capabilities
- **Backend**: Flask development server with `--reload` flag
- **Frontend**: Webpack dev server with Hot Module Replacement (HMR)
- **Configuration**: Development-optimized settings in `superset_config_dev.py`

### Development Configuration
The `superset_config_dev.py` file includes:
- SQLite database for easy setup
- CORS enabled for frontend development
- Debug mode enabled
- CSRF disabled for easier development
- Simple cache configuration
- Security features disabled for development

## 🛠 Making Changes

### Backend Changes
1. Edit Python files in the `superset/` directory
2. Changes will be automatically reloaded
3. Check the backend terminal for any errors

### Frontend Changes  
1. Edit React/TypeScript files in `superset-frontend/src/`
2. Changes will be automatically compiled and reloaded
3. Check the frontend terminal for compilation status

## 🔍 Troubleshooting

### Backend Issues
- Check if virtual environment is activated
- Verify `SUPERSET_CONFIG_PATH` environment variable
- Check backend terminal for error messages

### Frontend Issues
- Ensure Node.js dependencies are installed: `cd superset-frontend && npm ci`
- Check frontend terminal for compilation errors
- Clear browser cache if needed

### Database Issues
- Delete `superset.db` and run `superset db upgrade && superset init` to reset
- Recreate admin user with `superset fab create-admin`

## 📝 Additional Commands

### Database Management
```bash
# Activate virtual environment first
source venv/bin/activate
export SUPERSET_CONFIG_PATH=/path/to/superset_config_dev.py

# Reset database
superset db upgrade
superset init

# Create new admin user
superset fab create-admin
```

### Frontend Commands
```bash
cd superset-frontend

# Install dependencies
npm ci

# Run tests
npm test

# Lint code
npm run lint

# Build for production
npm run build
```

## 🎉 Success!

Your Superset development environment is now ready! You can start developing with hot reload enabled for both backend and frontend.

Happy coding! 🚀
